<% if action_name == 'index' %>
  <%
    title = weight_record.entity.name
    subtitle = I18n.l(weight_record.date, format: :abbreviated)
  %>
  <%= with_index_page_item(weight_record, { title:, subtitle: }) do |item| %>
    <% item.after_link do %>
      <h2 class="heading-card flex gap-1">
        <span><%= weight_record.display_value.round(1) %></span>
        <span class="text-gray-500"><%= weight_record.display_unit %></span>
      </h2>
    <% end %>
  <% end %>
<% else %>
  <h1>
    <span class="text-7xl"><%= @weight_record.display_value > 1000 ? format('%.3f', @weight_record.display_value) : @weight_record.display_value %></span>
    <span class="text-gray-500 font-normal"><%= @weight_record.display_unit %></span>
  </h1>

  <h4><span class="font-black"><%= I18n.l(@weight_record.date, format: :abbreviated) %></span></h4>

  <%= link_to @weight_record.entity, class: "row-span-2 flex gap-4 items-center w-fit rounded-md" do %>
    <%= render partial: 'shared/chicken_avatar', locals: { chicken: @weight_record.entity, klass: 'w-8 md:w-12' } %>
    <h2><%= @weight_record.entity.name %></h2>
  <% end %>
<% end %>
