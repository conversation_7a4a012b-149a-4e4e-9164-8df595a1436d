<!DOCTYPE html>
<html>
  <head>
    <title>App</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "tailwind", "inter-font", "data-turbo-track": "reload" %>

    <%= tag.link rel: "manifest", href: "/site.webmanifest" %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/luxon"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon"></script>

  <script>
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isFirefox = /Firefox/i.test(navigator.userAgent) && !/Android/i.test(navigator.userAgent);

    const isMacOS = /^((?!windows|linux|android).)*mac/i.test(navigator.platform);
    const isAndroid = /Android/i.test(navigator.userAgent);
  </script>

  <body class="relative">
    <dialog id="pleaseWait" class="hidden open:flex flex-col gap-8 md:gap-16 p-16 md:p-24 rounded-lg text-gray-800 backdrop:blue-800">
      <p class="text-xl font-semibold">Please wait...</p>
      <form method="dialog">
        <button>OK</button>
      </form>
    </dialog>

    <main class="container mx-auto py-20 px-1 md:px-5">
      <%= yield %>
    </main>

    <nav class="flex gap-7 items-center justify-between px-4 md:px-8 fixed top-0 transparent-header h-10 text-sm text-nowrap">
      <div class="h-full">
        <%= render 'layouts/main_nav_item', target: '/', icon: 'home-icon.svg', label: 'Home' %>
      </div>

      <div class="h-full flex gap-2">
        <%= render 'layouts/main_nav_item', target: chickens_path, icon: 'chicken-icon.svg', label: 'Chicken' %>
        <%= render 'layouts/main_nav_item', target: eggs_path, icon: 'eggs-icon.svg', label: 'Eggs' %>
        <% if current_user&.contributor_or_admin? %>
          <%= render 'layouts/main_nav_item', target: food_events_path, icon: 'meal-icon.svg', label: 'Meals' %>
          <%= render 'layouts/main_nav_item', target: weight_records_path, icon: 'scale-icon.svg', label: 'Weight' %>
        <% end %>
      </div>

      <div class="h-full">
        <%= render 'layouts/user_account_button', css: 'inline-block' %>
      </div>
    </nav>

    <% if flash[:alert] %>
      <div class="alert alert-danger fixed top-0 left-0 right-0 p-16 flex items-center bg-white drop-shadow-2xl z-30" role="alert">
        <div class="flex-1 flex items-center justify-center">
          <%= flash[:alert] %>
        </div>
        <button type="button"
          onclick="document.querySelector('.alert').style.display='none'"
          class="w-10 h-10 text-xl bg-gray-900 text-gray-50 rounded float-right">
          &times;
        </button>
      </div>
    <% end %>
  </body>

  <script>
    function pleaseWait() {
      document.querySelector('#pleaseWait').showModal();
    }

    function navigateTo(url) {
      pleaseWait();

      window.location.href = url.toString();
    }
  </script>
</html>
