<div class="w-full space-y-8" data-controller="eggs">
  <%= render 'shared/notice' %>

  <div class="flex items-center gap-8 p-2 mb-8 sticky top-10 z-10 bg-white/75 backdrop-blur-sm">
    <%= image_tag 'eggs-icon.svg', class: 'h-12' %>
    <h1 class="font-bold text-4xl flex-1"><%= "#{@chicken.name}’s " if @chicken.present? %>Eggs</h1>
    <%= render partial: 'shared/add_button', locals: { path: new_egg_path, klass: 'w-12' } if current_user&.contributor_or_admin? %>
  </div>

  <%
    statistics_prompt = "#{Egg.count} eggs over #{time_span_prompt(Egg.first.laid_at.midday, Egg.last.laid_at.midday)}."
  %>

  <%= turbo_frame_tag :statistics do %>
    <div class="portrait:hidden portrait:md:block h-96 overflow-auto space-y-2">
      <div class="flex gap-1">
        <button
          class="border-2 rounded-lg p-2 <%= @statistics == 'weight' ? 'bg-white' : 'bg-gray-200' %>"
          data-action="click->eggs#showWeightChart">
          Weight
        </button>
        <button
          class="border-2 rounded-lg p-2 <%= @statistics == 'width' ? 'bg-white' : 'bg-gray-200' %>"
          data-action="click->eggs#showWidthChart">
          Width
        </button>
      </div>

      <% if @statistics == 'width' && @categorized_widths.present? %>
        <%= render(
          partial: 'shared/chart',
          locals: {
            average_coordinates: @weekly_average_width,
            caption: statistics_prompt,
            categorized_coordinates: @categorized_widths,
            chart_id: 'eggsWidthChart',
            timespan_selector_id: 'eggs-width-chart-time-span-selector',
            vertical_axis_title: 'Width(mm)',
          },
        ) %>
      <% end %>

      <% if @statistics == 'weight' && @categorized_weights.present? %>
        <%= render(
          partial: 'shared/chart',
          locals: {
            average_coordinates: @weekly_average_weight,
            caption: statistics_prompt,
            categorized_coordinates: @categorized_weights,
            chart_id: 'eggsWeightChart',
            timespan_selector_id: 'eggs-weight-chart-time-span-selector',
            vertical_axis_title: 'Weight(g)',
          },
        ) %>
      <% end %>

      <% if @statistics.blank? %>
        <div class="flex items-center justify-center h-32 text-gray-500">
          <p>Click on Weight or Width to view the corresponding chart.</p>
        </div>
      <% end %>
    </div>
  <% end %>

  <div class="landscape:hidden text-sm text-gray-500"><%= statistics_prompt %></div>

  <form id="eggs-index-form" class="hidden">
    <input type="number" name="chicken_id" value="<%= @chicken&.id %>" autocomplete="off">
    <input type="number" name="year" value="<%= @year %>" autocomplete="off">
    <input type="number" name="month" value="<%= @month %>" autocomplete="off">
    <input type="number" name="day" value="<%= @day %>" autocomplete="off">
    <input type="checkbox" name="restrict_to_active_month" <%= 'checked' if @restrict_to_active_month %> autocomplete="off">
    <input type="text" name="statistics" value="<%= @statistics %>" autocomplete="off">
    <button
      type="submit"
      formmethod="get"
      data-eggs-target="updateGallery"
      data-turbo-frame="gallery">
      Gallery
    </button>
    <button
      type="submit"
      formmethod="get"
      data-eggs-target="updateCalendar"
      data-turbo-frame="calendar">
      Callendar
    </button>
    <button
      type="submit"
      formmethod="get"
      data-eggs-target="updateStatistics"
      data-turbo-frame="statistics">
      Statistics
    </button>
  </form>

  <div class="grid landscape:grid-cols-3 lg:grid-cols-3 gap-8">
    <div id="filters-box" class="flex flex-col gap-8 p-2 md:p-0 rounded-lg">
      <button id="filters-toggle"
        class="flex items-center gap-2 md:hidden font-thin cursor-pointer transition-all rounded p-4 -m-2 hover:bg-gray-100"
        onclick="toggleFiltersBox()">
        <%= image_tag 'filter-icon.svg', class: 'w-4 h-4' %>
        <span class="text-xs font-semibold">Filter by date and chicken</span>
        <span class="flex-1 flex justify-end">
          <%= image_tag 'plus-icon.svg', id: 'plus-icon', class: 'w-4 h-4' %>
          <%= image_tag 'minus-icon.svg', id: 'minus-icon', class: 'w-4 h-4 hidden' %>
        </span>
      </button>

      <div id="filters" class="hidden md:flex flex-col gap-2 transition-all">
        <select class="w-full rounded" data-action="change->eggs#restrictToChicken">
          <option
            <%= 'selected' unless @chicken.present? %>
            value="0">
            All chickens
          </option>

          <% Chicken.female.order(:name).find_each do |chicken| %>
            <option
              <%= 'selected' if chicken == @chicken %>
              value="<%= chicken.id %>">
              <%= chicken.name %>
            </option>
          <% end %>
        </select>

        <%= turbo_frame_tag :calendar do %>
          <div class="calendar flex-1 flex flex-col gap-2">
            <input
              type="month"
              id="datepicker"
              class="rounded"
              value="<%= @year %>-<%= 0 if @month < 10 %><%= @month %>"
              data-action="change->eggs#setMonth"
              autocomplete="off">
            <div id="day-swatched" class="@container grid grid-cols-7 gap-1">
              <% %w[Mon Tue Wed Thu Fri Sat Sun].each do |name| %>
                <div
                  class="cell day-name flex items-center justify-center bg-gray-100 text-gray-800 font-thin uppercase text-xs @md:text-base"
                  style="aspect-ratio: 1">
                  <%= name %>
                </div>
              <% end %>

              <%# !-- We want Monday to be the first day, therefore we subtract 1 -- %>
              <% first_day_of_month = @days.first.strftime("%w").to_i - 1 %>
              <% first_day_of_month.times do %>
                <div class="flex items-center justify-center bg-gray-100" style="aspect-ratio: 1"></div>
              <% end %>
              <% @days.each do |day| %>
                <% is_active_day = @day.present? && (day.day == @day) %>
                <% bg_color = is_active_day ? 'bg-yellow-500' : 'bg-gray-200' %>
                <% text_color = is_active_day ? 'text-black' : 'text-gray-800' %>

                <% has_eggs = @eggs_by_day.keys.include? day %>

                <div
                  class="
                    relative
                    flex items-center justify-center
                    <%= bg_color %> hover:bg-yellow-700
                    <%= text_color %> hover:text-white
                    text-xs @md:text-xl font-semibold
                    rounded transition-all
                    <%= 'pointer-events-none' unless has_eggs %>
                  "
                  <% if has_eggs %>
                    role="button"
                    data-action="click->eggs#setDay"
                    data-day="<%= day.day %>"
                  <% end %>
                  style="aspect-ratio: 1">
                  <% if has_eggs %>
                    <div class="absolute left-1 right-1 bottom-1 h-2 overflow-hidden">
                      <% @eggs_by_day[day].count.times do |time| %>
                        <%= image_tag 'egg-shape-icon.svg',
                          class: "h-2 absolute",
                          style: "left: #{0.2 * time}rem; z-index: #{@eggs_by_day[day].count - time}" %>
                      <% end %>
                    </div>
                  <% end %>

                  <span class="day"><%= day.day %></span>
                </div>
              <% end %>
              <% remaining_days = 7 - (@days.size + first_day_of_month) % 7 %>
              <% remaining_days.times do %>
                <div class="flex items-center justify-center bg-gray-100" style="aspect-ratio: 1"></div>
              <% end %>
            </div>
            <p class="hint text-xs text-gray-400 py-1">
              Click on a day to filter.
              <%= "Clicking on #{@day} will remove the filter" if @day.present? %>
            </p>
          </div>
        <% end %>
      </div>
    </div>

    <%= turbo_frame_tag :gallery do %>
      <div id="gallery" class="landscape:col-span-2 lg:col-span-2 flex flex-col gap-8">
        <div id="prompt">
          <% date = Date.new(@year, @month, @day || 1) %>

          <% if @day.present? && @chicken.present? %>
            Showing eggs laid by <%= @chicken.name %> in <%= I18n.l(date.to_date, format: :long) %>
          <% elsif @day.present? %>
            Showing eggs laid in <%= I18n.l(date.to_date, format: :long) %>
          <% else %>
            <% leier =  @chicken.present? ? @chicken.name : 'Bergamons' %>

            <% if @restrict_to_active_month %>
              Showing eggs leid by <%= leier %> in <%= Date::MONTHNAMES[@month] %>, <%= @year %>.
            <% else %>
              Showing all eggs ever leid by <%= leier %>.
            <% end %>
          <% end %>

          <label class="flex gap-2 items-center">
            <input
              type="checkbox"
              <%= 'checked' if @restrict_to_active_month %>
              data-action="change->eggs#restrictToActiveMonth">
            <div class="text-sm text-gray-700">
              Restrict to the active month: <span id="active-month-prompt"><%= Date::ABBR_MONTHNAMES[@month] %></span> <span id="active-year-prompt"><%= @year %></span>.
            </div>
          </label>
        </div>

        <div id="eggs" class="min-w-full grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 [&>*]:cursor-pointer">
          <%= render partial: 'egg_card', collection: @eggs, as: :egg, locals: { onclick: 'navigateToEgg' } %>
        </div>

        <%= will_paginate @eggs, class: 'text-center' %>
      </div>
    <% end %>
  </div>
</div>

<script>
  function askForEggId(event) {
    event.preventDefault();

    const id = window.prompt("Enter your desired egg id:");
    if (id === null || id === '' || isNaN(id)) return;

    window.location.href = `/eggs/${id}`;
  }

  if ((isSafari && isMacOS) || (isFirefox && !isAndroid)) {
    var datepicker = document.querySelector('#datepicker');
    datepicker.setAttribute('type', 'date');
  }

  function navigateToEgg(eggId) {
    navigateTo(`<%= eggs_url %>/${eggId}`);
  }

  function toggleFiltersBox() {
    const filters = document.querySelector('#filters');
    const plusIcon = document.querySelector('#plus-icon');
    const minusIcon = document.querySelector('#minus-icon');
    const filterBox = document.querySelector('#filters-box');
    const filtersToggle = document.querySelector('#filters-toggle');

    if (filters.classList.contains('hidden')) {
      filters.classList.remove('hidden');
      filters.classList.add('flex');

      plusIcon.classList.remove('hidden');
      minusIcon.classList.add('hidden');
    } else {
      filters.classList.remove('flex');
      filters.classList.add('hidden');

      plusIcon.classList.add('hidden');
      minusIcon.classList.remove('hidden');
    }

    filterBox.classList.toggle('outline');
    filterBox.classList.toggle('outline-2');

    filtersToggle.classList.toggle('font-thin');
  }
</script>
