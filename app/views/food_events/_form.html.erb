<% food_event = model %>

<%= render 'shared/model_form', model: food_event, locals: { data: { controller: 'food-event-form' } } do |form| %>
  <div class="my-5 flex gap-2">
    <%= form.collection_select(:food_type_id, @food_types, :id, :name, { prompt: "What type of food is it?" }, { class: "flex-1" }) %>
    <%= render partial: 'shared/add_button', locals: { path: new_food_type_path } if current_user&.contributor_or_admin? %>
  </div>

  <div class="my-5">
    <%= form.label :date, 'Cooked on' %>
    <%= form.datetime_field :date,
      class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full",
      data: {
        action: 'change->food-event-form#updateAvailableEggs',
        default_date: 'now'
      } %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.label :cooker_id, 'Cooked by' unless action_name == 'new' %>
    <%= form.collection_select(:cooker_id, @available_cookers, :id, :email, { prompt: "Who cooked this?" }, { class: "flex-1" }) %>
  </div>

  <div class="my-5 h-48 overflow-auto">
    <ul class="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-1 lg:gap-2">
      <%= form.button 'Update',
        formaction: food_event.persisted? ? edit_food_event_path(food_event) : new_food_event_path,
        formmethod: :get,
        style: 'display: none',
        data: {
          food_event_form_target: 'updateAvailableEggs',
          turbo_frame: :available_eggs
        } %>

      <%= turbo_frame_tag :available_eggs do %>
        <%= collection_check_boxes(:food_event, :egg_ids, @available_eggs, :id, :id, {}, { multiple: true }) do |choice| %>
          <li class="flex items-center gap-2 bg-gray-100 p-1 rounded">
            <%= choice.check_box class: 'p-4' %>
            <span class="flex-1 overflow-hidden text-ellipsis"><%= choice.label class: 'text-base' %></span>
          </li>
        <% end %>
      <% end %>
    </ul>
  </div>
<% end %>
