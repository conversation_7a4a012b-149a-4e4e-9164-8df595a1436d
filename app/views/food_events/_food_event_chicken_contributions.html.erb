<div class="flex <%= action_name == 'show' ? 'flex-col' : 'flex-row' %> gap-4">
  <div class="flex-1 text-end overflow-hidden">
    <div class="text-xs text-gray-500 mb-4">Eggs</div>
    <% if action_name == "show" %>
      <div class="flex flex-wrap font-mono gap-2">
        <% food_event.eggs.find_each do |egg| %>
          <%= link_to egg, class: 'rounded-sm overflow-hidden bg-gray-100 text-center flex' do %>
            <% egg.chickens.each do |chicken| %>
              <div class="w-1" style="background-color: <%= chicken.color %>"></div>
            <% end %>

            <span class="p-2"><%= egg.id %></span>
          <% end %>
        <% end %>
      </div>
    <% else %>
      <div class="block w-full overflow-hidden whitespace-nowrap text-ellipsis text-sm">
        <%= food_event.eggs.pluck(:id).join(', ') %>
      </div>
    <% end %>
  </div>

  <% if action_name == "show" %>
    <div class="flex-1 text-end overflow-hidden">
      <div class="text-xs text-gray-500 mb-4">Contributions</div>

      <div class="grid grid-cols-[3fr_auto_auto_auto_2fr] gap-2 items-center">
        <% @chicken_contributions.each do |chicken_id, data| %>
          <div class="relative h-1/2 rounded-sm overflow-hidden bg-slate-200">
            <div class="h-full rounded-sm bg-slate-500" style="width: <%= data[:normalized_contribution_rate] * 100 %>%"></div>
          </div>
          <span class="whitespace-nowrap text-right text-gray-500"><%= data[:contribution].round(1) %></span>
          <span class="whitespace-nowrap text-left text-gray-500"><%= data[:contribution] > 1 ? 'eggs' : 'egg' %></span>
          <%= render partial: 'shared/chicken_avatar', locals: { chicken: data[:chicken], klass: 'w-8 md:w-8' } %>
          <span
            class="font-semibold whitespace-nowrap w-full overflow-hidden text-ellipsis text-start"
            style="color: <%= data[:chicken].color %>">
            <%= data[:chicken].name %>
          </span>
        <% end %>
      </div>
    </div>
  <% end %>
</div>
