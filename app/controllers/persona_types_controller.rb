class PersonaTypesController < ApplicationController
  before_action :set_persona_type, only: %i[show edit update destroy]
  before_action :set_available_persona_types, only: %i[new create edit update]

  # GET /persona_types or /persona_types.json
  def index
    @persona_types = authorize PersonaType.all
                                          .then(&apply_pagination(per_page: 12))
  end

  # GET /persona_types/1 or /persona_types/1.json
  def show
    authorize @persona_type
  end

  # GET /persona_types/new
  def new
    @persona_type = authorize PersonaType.new
    @available_persona_types = PersonaType.order(:title)
  end

  # GET /persona_types/1/edit
  def edit
    authorize @persona_type
  end

  # POST /persona_types or /persona_types.json
  def create
    @persona_type = authorize PersonaType.new(persona_type_params)

    respond_to do |format|
      if @persona_type.save
        format.html { redirect_to persona_type_url(@persona_type), notice: 'Persona type was successfully created.' }
        format.json { render :show, status: :created, location: @persona_type }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @persona_type.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /persona_types/1 or /persona_types/1.json
  def update
    respond_to do |format|
      if (authorize @persona_type).update(persona_type_params)
        format.html { redirect_to persona_type_url(@persona_type), notice: 'Persona type was successfully updated.' }
        format.json { render :show, status: :ok, location: @persona_type }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @persona_type.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /persona_types/1 or /persona_types/1.json
  def destroy
    (authorize @persona_type).destroy!

    respond_to do |format|
      format.html { redirect_to persona_types_url, notice: 'Persona type was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_available_persona_types
    @available_persona_types = PersonaType.order(:title)
  end

  def set_persona_type
    @persona_type = PersonaType.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def persona_type_params
    params.require(:persona_type).permit(:title, :parent_id)
  end
end
