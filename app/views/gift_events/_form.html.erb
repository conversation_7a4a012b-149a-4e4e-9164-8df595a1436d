<% gift_event = model %>

<%= render 'shared/model_form', model: gift_event, locals: { data: { controller: 'gift-event-form'} } do |form| %>
  <div class="my-5">
    <%= form.label :date %>
    <%= form.datetime_field :date,
      class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full",
      data: {
        action: 'change->gift-event-form#updateAvailableEggs',
        default_date: 'now'
      } %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:persona_id, @available_personas, :id, :nickname, { prompt: "To whom was it gifted?" }, { class: "flex-1" }) %>
    <%= render partial: 'shared/add_button', locals: { path: new_persona_path } if current_user&.contributor_or_admin? %>
  </div>

  <div class="my-5 h-48 overflow-auto">
    <ul class="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-1 lg:gap-2">
      <%= form.button 'Update',
        formaction: gift_event.persisted? ? edit_gift_event_path(gift_event) : new_gift_event_path,
        formmethod: :get,
        style: 'display: none',
        data: {
          gift_event_form_target: 'updateAvailableEggs',
          turbo_frame: :available_eggs
        } %>

      <%= turbo_frame_tag :available_eggs do %>
        <%= collection_check_boxes(:gift_event, :egg_ids, @available_eggs, :id, :id, {}, { multiple: true }) do |choice| %>
          <li class="flex items-center gap-2 bg-gray-100 p-1 rounded">
            <%= choice.check_box class: 'p-4' %>
            <%= choice.label class: 'flex-1 overflow-hidden text-base' %>
          </li>
        <% end %>
      <% end %>
    </ul>
  </div>
<% end %>
