class WeightRecordsController < ApplicationController
  before_action :set_weight_record, only: %i[show edit update destroy]
  before_action :set_available_chickens, only: %i[new edit create update]

  # GET /weight_records or /weight_records.json
  def index
    @weight_records = authorize WeightRecord.order(date: :desc)
                                            .then(&apply_pagination(per_page: 12))

    results = Chicken.joins(:weight_records)
      .group('chickens.name')
      .pluck(Arel.sql('chickens.name, array_agg(ROW(weight_records.date, weight_records.value, chickens.color) ORDER BY weight_records.date)'))
      .each_with_object({}) do |(chicken_name, records), hash|
      records_array = records.scan(/\(([^,]+),(\d+),(#\w\w\w\w\w\w)\)/)

      color = records_array.first.last
      coordinates = records_array.map do |date_str, value, _|
        { x: Time.parse(date_str).beginning_of_day.iso8601, y: value.to_i / 1000.0 }
      end

      hash[chicken_name] = { color:, coordinates: }
    end

    @chicken_weight_records_categorized_coordinates = results

    @chicken_weight_averages = WeightRecord.order(Arel.sql("DATE_TRUNC('week', date)"))
      .group(Arel.sql("DATE_TRUNC('week', date)"))
      .having('COUNT(*) > 0')
      .average(:value)

    @chicken_weight_averages.transform_values! { |value| value / 1000.0 }
  end

  # GET /weight_records/1 or /weight_records/1.json
  def show
    authorize @weight_record

    if @weight_record.entity.is_a? Chicken
      chicken = @weight_record.entity

      @chicken_weight_categorized_coordinates = {
        chicken.name => {
          color: chicken.color,
          coordinates: chicken.weight_records
                              .order(:date)
                              .pluck(:date, :value)
                              .map { |date, weight| { x: date.to_time.iso8601, y: weight } }
        }
      }
    end
  end

  # GET /weight_records/new
  def new
    @weight_record = authorize WeightRecord.new
  end

  # GET /weight_records/1/edit
  def edit; end

  # POST /weight_records or /weight_records.json
  def create
    @weight_record = authorize WeightRecord.new(weight_record_params.merge({ entity_type: 'Chicken' }))

    respond_to do |format|
      if @weight_record.save
        format.html { redirect_to weight_record_url(@weight_record), notice: 'Weight record was successfully created.' }
        format.json { render :show, status: :created, location: @weight_record }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @weight_record.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /weight_records/1 or /weight_records/1.json
  def update
    authorize @weight_record
    respond_to do |format|
      if @weight_record.update(weight_record_params)
        format.html { redirect_to weight_record_url(@weight_record), notice: 'Weight record was successfully updated.' }
        format.json { render :show, status: :ok, location: @weight_record }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @weight_record.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /weight_records/1 or /weight_records/1.json
  def destroy
    (authorize @weight_record).destroy!

    respond_to do |format|
      format.html { redirect_to weight_records_url, notice: 'Weight record was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_weight_record
    @weight_record = WeightRecord.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def weight_record_params
    params.fetch(:weight_record, {}).permit(:entity_id, :entity_type, :date, :value)
  end

  def set_available_chickens
    date = weight_record_params[:date].presence || @weight_record&.date || Time.zone.today
    date = DateTime.parse(date) if date.is_a? String

    @available_chickens = authorize Chicken.order(:name).alive(date)
  end
end
