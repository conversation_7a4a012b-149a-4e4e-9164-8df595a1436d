<% refuel_event = model %>

<%= render 'shared/model_form', model: refuel_event do |form| %>
  <div class="my-5 flex gap-2">
    <%= form.collection_select(:refuel_station_id, RefuelStation.order(:nickname), :id, :nickname, { prompt: "Where?" }, { class: "flex-1" }) %>
    <%= render partial: 'shared/add_button', locals: { path: new_refuel_station_path } if current_user&.contributor_or_admin? %>
  </div>

  <div class="my-5">
    <%= form.label :refuled_at %>
    <%= form.datetime_field :refuled_at, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full", 'data-default-date': 'now' %>
  </div>

  <div class="my-5">
    <%= form.label :amount %>
    <%= form.number_field :amount, step: "any", min: "0", class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:unit_id, Unit.order(:name), :id, :name, { prompt: "Which unit?" }, { class: "flex-1" }) %>
    <%= render partial: 'shared/add_button', locals: { path: new_unit_path } if current_user&.contributor_or_admin? %>
  </div>

  <div class="my-5">
    <%= form.label :paid_price %>
    <%= form.number_field :paid_price, step: "any", min: "0", class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5">
    <%= form.label :price_per_unit %>
    <%= form.number_field :price_per_unit, step: "any", min: "0", class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:currency_id, Currency.order(:name), :id, :name, { prompt: "Which currency?" }, { class: "flex-1" }) %>
    <%= render partial: 'shared/add_button', locals: { path: new_currency_path } if current_user&.contributor_or_admin? %>
  </div>
<% end %>
