require 'test_helper'

class RefuelStationsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @refuel_station = refuel_stations(:one)
  end

  test 'should get index' do
    get refuel_stations_url
    assert_response :success
  end

  test 'should get new' do
    get new_refuel_station_url
    assert_response :success
  end

  test 'should create refuel_station' do
    assert_difference('RefuelStation.count') do
      post refuel_stations_url,
           params: { refuel_station: {
                                       brand_id: @refuel_station.brand_id, latitude: 1, longitude: 1,
                                       nickname: @refuel_station.nickname
                                     }
                   }
    end

    assert_redirected_to refuel_station_url(RefuelStation.last)
  end

  test 'should show refuel_station' do
    get refuel_station_url(@refuel_station)
    assert_response :success
  end

  test 'should get edit' do
    get edit_refuel_station_url(@refuel_station)
    assert_response :success
  end

  test 'should update refuel_station' do
    patch refuel_station_url(@refuel_station), params: { refuel_station: {
                                                                           brand_id: @refuel_station.brand_id,
                                                                           latitude: 2,
                                                                           longitude: 2,
                                                                           nickname: @refuel_station.nickname
                                                                         }
                                                        }
    assert_redirected_to refuel_station_url(@refuel_station)
  end

  test 'should destroy refuel_station' do
    assert_difference('RefuelStation.count', -1) do
      delete refuel_station_url(@refuel_station)
    end

    assert_redirected_to refuel_stations_url
  end
end
