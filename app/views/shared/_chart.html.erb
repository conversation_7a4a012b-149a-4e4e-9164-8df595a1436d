<%
  average_coordinates ||= nil
  camelized_chart_id = chart_id.split('-').map(&:camelize).join
  caption ||= nil
  chart_title ||= nil
  chart_type ||= 'line'
  labels ||= nil
  vertical_axis_title ||= 'Weight(g)'

  timespan_options = ChartTimespanOptions.all
  default_timespan ||= ChartTimespanOptions::LAST_MONTH
%>

<figure
  id="chart-<%= chart_id %>"
  class="relative border-2 rounded-md overflow-auto flex flex-col gap-2 items-center bg-orange-50 shadow-inner p-4">
  <% if chart_title.present? %>
    <heading class="w-full block text-center font-semibold sticky top-0 p-4 bg-gray-900 text-orange-50"><%= chart_title %></heading>
  <% end %>

  <select
    class="min-w-36 px-4 bg-gray-200 text-gray-800 <%= 'mt-4' unless chart_title.present? %>"
    id="<%= timespan_selector_id %>"
    onchange="onTimeSpanSelect<%= camelized_chart_id %>(this)">
    <% timespan_options.each do |value, label| %>
      <option value="<%= value %>" <%= 'selected' if value == default_timespan %>><%= label %></option>
    <% end %>
  </select>

  <div class="p-4 w-full">
    <canvas id="<%= chart_id %>"></canvas>
  </div>

  <% if caption.present? %>
    <figcaption class="text-xs md:text-sm text-black/50">
      <button onclick="onTimeSpanSelect<%= camelized_chart_id %>({ value: 'ENTIRE_HISTORY'})"><%= caption %></button>
    </figcaption>
  <% end %>
</figure>

<script>
  function filterByTimespan<%= camelized_chart_id %>(coordinatesHashArray, timespan = 'ENTIRE_HISTORY') {
    const ONE_MONTH = 1 * 30 * 24 * 3600 * 1000;
    const MAXIMUM_ALLOWED = {
      LAST_10_DAYS: ONE_MONTH / 3,
      LAST_MONTH: ONE_MONTH,
      LAST_3_MONTHS: 3 * ONE_MONTH,
      LAST_6_MONTHS: 6 * ONE_MONTH,
      LAST_12_MONTHS: 12 * ONE_MONTH,
    };

    return timespan === 'ENTIRE_HISTORY'
      ? coordinatesHashArray
      : coordinatesHashArray.filter(({ x, y }) => new Date() - Date.parse(x) < MAXIMUM_ALLOWED[timespan]);
  }

  function unitByTimespan(timespan = 'ENTIRE_HISTORY') {
    switch(timespan){
      case 'LAST_10_DAYS':
      case 'LAST_MONTH': return 'day';
      case 'LAST_3_MONTHS': return 'week';
      case 'LAST_6_MONTHS':
      case 'LAST_12_MONTHS':
      default: return 'month';
    }
  }

  function drawChart<%= camelized_chart_id %>(chartId, timespan = 'LAST_10_DAYS') {
    const ctx = document.getElementById(chartId)?.getContext('2d');

    if (!ctx) return;

    Chart.getChart(chartId)?.destroy();

    const unfilteredDatasets = [
      <% categorized_coordinates.keys.each do |key| %>
        {
          label: '<%= key %>',
          data: filterByTimespan<%= camelized_chart_id %>(
            <%= categorized_coordinates[key][:coordinates].to_json.html_safe %>,
            timespan
          ), // Vertical axis (weight)
          borderColor: '<%= categorized_coordinates[key][:color] %>',
          fill: false,
          tension: 0.1
        },
      <% end %>
    ];

    const filteredDatasets = unfilteredDatasets.filter(dataset => dataset.data.length > 0);

    new Chart(ctx, {
      type: '<%= chart_type %>',
      data: {
        <% if labels %>
          labels: <%= labels.to_json.html_safe %>, // Horizontal axis (time)
        <% end %>
        datasets: [
          ...filteredDatasets,
          <% if average_coordinates %>
            {
              label: 'Average',
              data: filterByTimespan<%= camelized_chart_id %>(
                <%= average_coordinates.entries.map { |date, average_value| { x: date.to_time.iso8601, y: average_value } }.to_json.html_safe %>,
                timespan
              ), // Vertical axis (weight)
              borderColor: 'rgba(0, 0, 0, 1)',
              borderDash: [5, 5],
              fill: true,
              tension: 0.1
            },
          <% end %>
        ],
      },
      options: {
        scales: {
          x: {
            type: 'time',
            time: {
              unit: unitByTimespan(timespan),  // Adjust granularity as needed (can be 'day', 'month', etc.)
              // tooltipFormat: 'YYYY/MM/DD',
              displayFormats: {
                day: 'MMM dd, ’yy'  // Customize the display format on the axis
              },
            },
            title: {
              display: true,
              text: 'Date',
            },
          },
          y: {
            title: {
              display: true,
              text: '<%= vertical_axis_title %>',
            },
          },
        },
      },
    });
  }

  function onTimeSpanSelect<%= camelized_chart_id %>(selectedOption) {
    const timespan = selectedOption.value;

    drawChart<%= camelized_chart_id %>('<%= chart_id %>', timespan);
  }

  document.addEventListener("turbo:load", function() {
    const timespan = document.querySelector('#<%= timespan_selector_id %>')?.value;

    drawChart<%= camelized_chart_id %>('<%= chart_id %>', timespan);
  });
</script>
