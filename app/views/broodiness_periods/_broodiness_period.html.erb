<%
  chicken = broodiness_period.chicken

  subtitle_start_day = broodiness_period.start_day.present? ? I18n.l(broodiness_period.start_day, format: :abbreviated) : 'Unknown'
  subtitle_end_day = broodiness_period.end_day.present? ? I18n.l(broodiness_period.end_day, format: :abbreviated) : 'Unknown'

  subtitle = [subtitle_start_day, subtitle_end_day].join(' - ')
%>

<% if action_name == 'index' %>
  <%= with_index_page_item(broodiness_period, { title: broodiness_period.chicken.name, subtitle: }) %>
<% else %>
  <div class="space-y-8 p-2">
    <h1><%= subtitle %></h1>

    <%= link_to chicken, class: "row-span-2 flex gap-4 items-center w-fit rounded-md" do %>
      <%= render 'shared/chicken_avatar', chicken:, class: 'w-8 md:w-12' %>
      <h2><%= chicken.name %></h2>
    <% end %>
  </div>
<% end %>
