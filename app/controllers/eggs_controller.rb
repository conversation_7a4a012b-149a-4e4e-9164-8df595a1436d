class EggsController < ApplicationController
  before_action :set_egg, only: %i[show edit update destroy siblings]
  before_action :set_available_chickens, only: %i[new edit update]

  # GET /eggs or /eggs.json
  def index
    @year  = param_missing?(:year) ? Date.current.year : params[:year].to_i
    @month = param_missing?(:month) ? Date.current.month : params[:month].to_i
    @day   = param_missing?(:day) ? nil : params[:day].to_i

    date = Date.new(@year, @month, @day || 1)

    @chicken = Chicken.find_by(id: params[:chicken_id])
    @statistics = params[:statistics]

    # Only load chart data if statistics parameter is present
    case @statistics
    when 'weight'
      @categorized_weights = categorized_weights
      @weekly_average_weight = weekly_average_weight
    when 'width'
      @categorized_widths = categorized_widths
      @weekly_average_width = if @chicken.present?
        first_day, last_day = @chicken.eggs.pluck('MIN(laid_at)', 'MAX(laid_at)').flatten
        weekly_average_width(first_day.beginning_of_day..last_day.end_of_day)
      else
        weekly_average_width
      end
    end

    @restrict_to_active_month = params[:restrict_to_active_month].present?

    eggs = @chicken&.eggs || Egg.all
    eggs = eggs.order(laid_at: :desc)

    @eggs = eggs
    @eggs = @eggs.where(laid_at: date.all_month) if @restrict_to_active_month
    @eggs = @eggs.where(laid_at: date.all_day) if @day.present?
    @eggs = @eggs.then(&apply_pagination(per_page: 12))

    @days = date.all_month.to_a
    @eggs_by_day = eggs.where(laid_at: date.all_month).group_by { |egg| egg.laid_at.to_date }
  end

  # GET /eggs/1 or /eggs/1.json
  def show
    defects = []
    defects << 'Juan José' if @egg.juan_jose_defect
    defects << 'Nader' if @egg.nader_defect
    defects << 'Shahbagha' if @egg.shahbagha_defect

    @defects = defects
  end

  # GET /eggs/new
  def new
    @egg = Egg.new egg_params

    authorize @egg
  end

  # GET /eggs/1/edit
  def edit
    authorize @egg
  end

  # POST /eggs or /eggs.json
  def create
    @egg = Egg.new(egg_params)

    authorize @egg

    respond_to do |format|
      if @egg.save
        format.html { redirect_to egg_url(@egg), notice: I18n.t('flash.notice.item_created', item: 'Egg') }
        format.json { render :show, status: :created, location: @egg }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @egg.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /eggs/1 or /eggs/1.json
  def update
    authorize @egg

    # Preserve chicken associations if not explicitly provided in params
    if params[:egg] && !params[:egg].key?(:chicken_ids)
      params[:egg][:chicken_ids] = @egg.chicken_ids
    end

    respond_to do |format|
      if @egg.update(egg_params)
        format.html { redirect_to egg_url(@egg), notice: I18n.t('flash.notice.item_created', item: 'Egg') }
        format.json { render :show, status: :ok, location: @egg }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @egg.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /eggs/1 or /eggs/1.json
  def destroy
    authorize @egg

    @egg.destroy

    respond_to do |format|
      format.html { redirect_to eggs_url, notice: I18n.t('flash.notice.item_destroyed', item: 'Egg') }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_egg
    @egg = Egg.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def egg_params
    params.fetch(:egg, {}).permit(
      :chicken,
      :content_health,
      :day,
      :double_yolked,
      :fertilized,
      :height,
      :juan_jose_defect,
      :laid_at,
      :laid_at_time_unknown,
      :month,
      :mahlagha_defect,
      :nader_defect,
      :restrict_to_active_month,
      :shahbagha_defect,
      :shape_health,
      :skin_health,
      :statistics,
      :unknown_fate,
      :weight,
      :width,
      :year,
      chicken_ids: [],
      images: []
    )
  end

  def set_available_chickens
    date = egg_params[:laid_at].presence || @egg&.laid_at.presence || Time.zone.today
    date = DateTime.parse(date) if date.is_a? String

    egg_chicken_hash = Egg.where(laid_at: date.all_day)
      .joins(:chickens)
      .group('eggs.id')
      .having('COUNT(chickens.id) = 1')
      .pluck('eggs.id, array_agg(chickens.id)')
      .to_h

    already_laid_chickens = egg_chicken_hash.values.flatten
    already_laid_chickens -= @egg.chickens.pluck(:id) if @egg.present?

    @available_chickens = Chicken
      .laying(at: date)
      .where.not(id: already_laid_chickens)
      .order(:name)
  end

  def categorized_widths
    data = Egg.where.not(width: nil)
      .order(:laid_at)
      .joins(:chickens)

    data = data.where(chickens: { name: @chicken.name }) if @chicken.present?

    grouped_data = data.group('eggs.id').having('COUNT(chickens.id) = 1')

    grouped_data.pluck('eggs.laid_at, array_agg(chickens.name), array_agg(chickens.color), eggs.width')
      .map { |date, names, colors, width| [ date, names.first, colors.first, width ] }
      .group_by { |_, name, _, _| name }
      .transform_values do |entries|
      {
        color: entries.first[2] || '#FFFF00', # Extract color from the first entry
        coordinates: entries.map { |x, _, _, y| { x:, y: y.to_f } } # Map x and y coordinates
      }
    end
  end

  def categorized_weights
    data = Egg.weighted
      .order(:laid_at)
      .joins(:chickens)

    data = data.where(chickens: { name: @chicken.name }) if @chicken.present?

    grouped_data = data.group('eggs.id').having('COUNT(chickens.id) = 1')

    grouped_data.pluck('eggs.laid_at, array_agg(chickens.name), array_agg(chickens.color), eggs.weight')
      .map { |date, names, colors, weight| [ date, names.first, colors.first, weight ] }
      .group_by { |_, name, _, _| name }
      .transform_values do |entries|
      {
        color: entries.first[2] || '#FFFF00', # Extract color from the first entry
        coordinates: entries.map { |x, _, _, y| { x:, y: y.to_f } } # Map x and y coordinates
      }
    end
  end

  def set_daily_average_weight
    @daily_average_weight = Egg.weighted.order('DATE(laid_at)').group('DATE(laid_at)').having('COUNT(*) > 0').average(:weight)
  end

  def weekly_average_width(timespan = nil)
    eggs = Egg.where.not(width: nil)
    eggs = eggs.where(laid_at: timespan) if timespan.present?

    eggs.order(Arel.sql("DATE_TRUNC('week', laid_at)"))
      .group(Arel.sql("DATE_TRUNC('week', laid_at)"))
      .having('COUNT(*) > 0')
      .average(:width)
  end

  def weekly_average_weight
    Egg.weighted
      .order(Arel.sql("DATE_TRUNC('week', laid_at)"))
      .group(Arel.sql("DATE_TRUNC('week', laid_at)"))
      .having('COUNT(*) > 0')
      .average(:weight)
  end
end
