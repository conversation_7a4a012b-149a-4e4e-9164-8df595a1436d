class UnitsController < ApplicationController
  before_action :set_unit, only: %i[show edit update destroy]

  # GET /units or /units.json
  def index
    @units = authorize Unit.all
  end

  # GET /units/1 or /units/1.json
  def show
    authorize @unit
  end

  # GET /units/new
  def new
    @unit = authorize Unit.new
  end

  # GET /units/1/edit
  def edit
    authorize @unit
  end

  # POST /units or /units.json
  def create
    @unit = authorize Unit.new(unit_params)

    respond_to do |format|
      if (authorize @unit).save
        format.html { redirect_to unit_url(@unit), notice: 'Unit was successfully created.' }
        format.json { render :show, status: :created, location: @unit }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @unit.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /units/1 or /units/1.json
  def update
    respond_to do |format|
      if (authorize @unit).update(unit_params)
        format.html { redirect_to unit_url(@unit), notice: 'Unit was successfully updated.' }
        format.json { render :show, status: :ok, location: @unit }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @unit.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /units/1 or /units/1.json
  def destroy
    (authorize @unit).destroy!

    respond_to do |format|
      format.html { redirect_to units_url, notice: 'Unit was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_unit
    @unit = Unit.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def unit_params
    params.require(:unit).permit(:name, :description, :sign, :unit_category_id)
  end
end
