class VetVisitsController < ApplicationController
  before_action :set_vet_visit, only: %i[show edit update destroy]
  before_action :prepare, only: %i[create new edit update]

  # GET /vet_visits or /vet_visits.json
  def index
    @vet_visits = authorize VetVisit.order(date: :desc)
                                    .then(&apply_pagination(per_page: 12))
  end

  # GET /vet_visits/1 or /vet_visits/1.json
  def show
    authorize @vet_visit
  end

  # GET /vet_visits/new
  def new
    @vet_visit = authorize VetVisit.new
  end

  # GET /vet_visits/1/edit
  def edit
    authorize @vet_visit
  end

  # POST /vet_visits or /vet_visits.json
  def create
    @vet_visit = authorize VetVisit.new(vet_visit_params)

    respond_to do |format|
      if @vet_visit.save
        format.html { redirect_to vet_visit_url(@vet_visit), notice: 'Vet visit was successfully created.' }
        format.json { render :show, status: :created, location: @vet_visit }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @vet_visit.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /vet_visits/1 or /vet_visits/1.json
  def update
    respond_to do |format|
      if (authorize @vet_visit).update(vet_visit_params)
        format.html { redirect_to vet_visit_url(@vet_visit), notice: 'Vet visit was successfully updated.' }
        format.json { render :show, status: :ok, location: @vet_visit }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @vet_visit.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /vet_visits/1 or /vet_visits/1.json
  def destroy
    (authorize @vet_visit).destroy!

    respond_to do |format|
      format.html { redirect_to vet_visits_url, notice: 'Vet visit was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  # Only allow a list of trusted parameters through.
  def vet_visit_params
    params
      .fetch(:vet_visit, {})
      .permit(
        :at_home,
        :chicken_id,
        :date,
        :doctor_verdict,
        :has_medicine_routine,
        :injection_in_place,
        :medicine_in_place,
        :needs_second_visit,
        :prescription,
        :previous_visit_id,
        :severity,
        :situation_description,
        :vet_clinic_id,
        :vet_id
      )
  end

  # Use callbacks to share common setup or constraints between actions.
  def set_vet_visit
    @vet_visit = VetVisit.find(params[:id])
  end

  def prepare
    set_available_chickens
    set_available_previous_visits
    set_available_vet_clinics
    set_available_vets
  end

  def set_available_chickens
    date = vet_visit_params[:date].presence || @vet_visit&.date || Time.zone.today
    date = DateTime.parse(date) if date.is_a? String

    @available_chickens = Chicken.order(:name).alive(date)
  end

  def set_available_previous_visits
    already_followed_up_visits = VetVisit.distinct.pluck(:previous_visit_id).compact

    @available_previous_visits = VetVisit.where.not(id: already_followed_up_visits).order(:date)

    return if @vet_visit.blank?

    @available_previous_visits = @available_previous_visits
      .where.not(id: @vet_visit.id)
      .where(chicken_id: @vet_visit.chicken_id)
      .where('date < ?', @vet_visit.date)
      .or(VetVisit.where(id: @vet_visit.previous_visit_id))
  end

  def set_available_vets
    @available_vets = Persona.order(:nickname)
  end

  def set_available_vet_clinics
    @available_vet_clinics = VetClinic.order(:name)
  end
end
