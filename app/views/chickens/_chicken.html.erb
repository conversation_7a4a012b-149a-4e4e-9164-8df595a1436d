<div id="<%= dom_id chicken %>" class="grid <%= 'md:grid-cols-2' if action_name == 'show' %> gap-8">
  <div class="row-span-full bg-gradient-to-b from-stone-50 to-stone-200 w-fit p-8 rounded-lg flex flex-col items-center gap-4 border-2">
    <%= image_tag chicken.avatar, width: '100%', class: "#{chicken.has_own_avatar? ? 'border' : ''} #{'p-2' unless chicken.has_own_avatar?}", loading: 'lazy' %>
  </div>

  <div class="space-y-4 px-4 md:px-0">
    <h1><%= chicken.name %></h1>

    <div class="flex flex-col gap-1">
      <small class="text-sm">Gender</small>
      <div class="font-bold text-xl capitalize flex items-center gap-4">
        <% if chicken.has_own_avatar? %>
          <%= image_tag chicken.male? ? Chicken.male_avatar : Chicken.female_avatar, class: 'h-6', loading: 'lazy' %>
        <% end %>
        <h3><%= chicken.gender.name %></h3>
      </div>
    </div>

    <% if chicken.age.present? %>
      <small><%= chicken.age %> <%= chicken.death_day.present? ? 'lived' : 'old' %></small>
    <% end %>

    <% if chicken.birth_day.present? %>
      <div>
        <small class="block text-sm">Born on</small>
        <h3><%= I18n.l(chicken.birth_day, format: :abbreviated) %></h3>
      </div>
    <% end %>

    <% if chicken.death_day.present? %>
      <div class="flex flex-col gap-1">
        <small class="block text-sm">Passed away on</small>
        <h3><%= I18n.l(chicken.death_day, format: :abbreviated) %></h3>
      </div>
    <% end %>

    <% if chicken.join_day.present? %>
      <div class="flex flex-col gap-1">
        <small class="block text-sm">Joined on</small>
        <h3><%= I18n.l(chicken.join_day, format: :abbreviated) %></h3>
      </div>
    <% end %>

    <% if chicken.leave_day.present? %>
      <div>
        <small class="block text-sm">Left on</small>
        <h3><%= I18n.l(chicken.leave_day, format: :abbreviated) %></h3>
      </div>
    <% end %>

    <div class="flex flex-wrap gap-2 [&>*]:rounded [&>*]:border [&>*]:p-1 [&>*]:md:p-4">
      <% if chicken.female? %>
        <%= link_to chicken_eggs_path(chicken) do %>
          <div class="flex items-center gap-2">
            <%= image_tag 'eggs-icon.svg', class: 'w-6', loading: 'lazy' %>
            <span class="font-semibold text-2xl"><%= chicken.eggs.count %></span>
          </div>
        <% end %>
      <% end %>

      <div class="flex items-center gap-2">
        <%= image_tag 'meal-icon.svg', class: 'w-6', loading: 'lazy' %>
        <span class="font-semibold text-2xl"><%= chicken.eggs.joins(:food_event).distinct.count(:food_event_id) %></span>
      </div>

      <div class="flex items-center gap-2">
        <%= image_tag 'gift-icon.svg', class: 'w-6', loading: 'lazy' %>
        <span class="font-semibold text-2xl"><%= chicken.eggs.joins(:gift_event).distinct.count(:gift_event_id) %></span>
      </div>
    </div>
  </div>

  <% if action_name != "show" %>
    <%= link_to "Show this chicken", chicken, class: "rounded-lg py-3 px-5 bg-gray-100 inline-block font-medium" %>
    <%= link_to 'Edit this chicken', edit_chicken_path(chicken), class: "rounded-lg py-3 ml-2 px-5 bg-gray-100 inline-block font-medium" %>
  <% end %>
</div>
