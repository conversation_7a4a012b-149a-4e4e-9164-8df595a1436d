<%
  scope  ||= nil
  url    ||= nil
  format ||= nil

  locals ||= {}
  locals.merge!({ class: 'contents' }) unless locals.has_key?(:class)
%>

<script>
  function pad(n) {
    return n.toString().padStart(2, '0');
  }

  function now() {
    const now = new Date();

    // Format as "YYYY-MM-DDTHH:MM" for datetime-local input
    const year = now.getFullYear();

    const month   = pad(now.getMonth() + 1);
    const day     = pad(now.getDate());
    const hour    = pad(now.getHours());
    const minutes = pad(now.getMinutes());

    return `${year}-${month}-${day}T${hour}:${minutes}`;
  }

  function setNow() {
    const currentDateTime = now();

    document.querySelectorAll('[data-default-date="now"]').forEach(element => {
      element.value = currentDateTime;
    });
  }

  // Support both normal navigation and Turbo (or Turbolinks) navigation
  function onPageLoad() {
    <% unless model.persisted? %>
      setNow();
    <% end %>
  }

  document.addEventListener('DOMContentLoaded', onPageLoad);
  document.addEventListener('turbo:load', onPageLoad);
  document.addEventListener('turbolinks:load', onPageLoad);
</script>

<%= form_with(model: model, scope: scope, url: url, format: format, **locals) do |form| %>
  <% if model.errors.any? %>
    <div id="error_explanation" class="bg-red-50 text-red-500 px-3 py-2 font-medium rounded-lg mt-3">
      <h2><%= pluralize(model.errors.count, "error") %> prohibited this <%= model.class %> from being saved:</h2>

      <ul>
        <% model.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <%= yield(form) %>

  <%= render partial: 'shared/form_action_buttons', locals: { model: } %>
<% end %>
