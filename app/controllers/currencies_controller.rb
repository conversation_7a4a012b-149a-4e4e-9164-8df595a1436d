class CurrenciesController < ApplicationController
  before_action :set_currency, only: %i[show edit update destroy]

  # GET /currencies or /currencies.json
  def index
    @currencies = authorize Currency.all
                                    .then(&apply_pagination(per_page: 12))
  end

  # GET /currencies/1 or /currencies/1.json
  def show
    authorize @currency
  end

  # GET /currencies/new
  def new
    @currency = authorize Currency.new
  end

  # GET /currencies/1/edit
  def edit
    authorize @currency
  end

  # POST /currencies or /currencies.json
  def create
    @currency = authorize Currency.new(currency_params)

    respond_to do |format|
      if @currency.save
        format.html { redirect_to currency_url(@currency), notice: 'Currency was successfully created.' }
        format.json { render :show, status: :created, location: @currency }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @currency.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /currencies/1 or /currencies/1.json
  def update
    respond_to do |format|
      if (authorize @currency).update(currency_params)
        format.html { redirect_to currency_url(@currency), notice: 'Currency was successfully updated.' }
        format.json { render :show, status: :ok, location: @currency }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @currency.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /currencies/1 or /currencies/1.json
  def destroy
    (authorize @currency).destroy!

    respond_to do |format|
      format.html { redirect_to currencies_url, notice: 'Currency was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_currency
    @currency = Currency.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def currency_params
    params.require(:currency).permit(:name, :code, :sign)
  end
end
