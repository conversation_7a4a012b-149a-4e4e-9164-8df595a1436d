<% purchase_event = model %>

<%= render 'shared/model_form', model: purchase_event do |form| %>
  <div class="my-5 flex gap-2">
    <%= form.collection_select(:buyer_id, @available_buyers, :id, :nickname, { prompt: "Who bought it?" }, { class: "flex-1" }) %>
    <%= render partial: 'shared/add_button', locals: { path: new_persona_path } if current_user&.contributor_or_admin? %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:seller_id, @available_sellers, :id, :nickname, { prompt: "Who sold it?" }, { class: "flex-1" }) %>
    <%= render partial: 'shared/add_button', locals: { path: new_persona_path } if current_user&.contributor_or_admin? %>
  </div>

  <div class="my-5">
    <%= form.label :purchased_at %>
    <%= form.datetime_field :purchased_at, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <h3>Products</h3>
  <div id="products-container">
    <% @purchase_event.purchase_event_products.each do |purchase_event_product| %>
      <%= form.fields_for :purchase_event_products, purchase_event_product do |product_form| %>
        <%= product_form.collection_select :product_id,  @available_products,   :id, :name, { prompt: "Select product" },  class: "w-full" %>
        <%= product_form.collection_select :unit_id,     Unit.order(:name),     :id, :name, { prompt: "Select Unit" },     class: "w-full mt-2", placeholder: "Unit" %>
        <%= product_form.collection_select :currency_id, Currency.order(:name), :id, :name, { prompt: "Select Currency" }, class: "w-full mt-2", placeholder: "Unit" %>
        <%= product_form.number_field      :quantity,        class: "w-full mt-2", placeholder: "Quantity" %>
        <%= product_form.number_field      :paid_price,      class: "w-full mt-2", placeholder: "Paid Price" %>
        <%= product_form.number_field      :original_price,  class: "w-full mt-2", placeholder: "Original Price (optional)" %>
        <%= product_form.date_field        :production_date, class: "w-full mt-2", placeholder: "Production Date" %>
        <%= product_form.date_field        :expiry_date,     class: "w-full mt-2", placeholder: "Expiry Date" %>

        <div class="mt-2">
          <%= product_form.check_box :_destroy %>
          <%= product_form.label :_destroy, "Remove this product" %>
        </div>
      <% end %>
    <% end %>
  </div>

  <!-- Hidden template for adding new products dynamically -->
  <div data-template="product" class="product-fields border p-2 my-2 hidden">
    <%= form.fields_for :purchase_event_products, PurchaseEventProduct.new do |product_form| %>
      <%= product_form.collection_select :product_id,  @available_products,   :id, :name, { prompt: "Select product"  }, class: "w-full" %>
      <%= product_form.collection_select :unit_id,     Unit.order(:name),     :id, :name, { prompt: "Select Unit"     }, class: "w-full mt-2", placeholder: "Unit" %>
      <%= product_form.collection_select :currency_id, Currency.order(:name), :id, :name, { prompt: "Select Currency" }, class: "w-full mt-2", placeholder: "Unit" %>
      <%= product_form.number_field      :quantity,        class: "w-full mt-2", placeholder: "Quantity" %>
      <%= product_form.number_field      :paid_price,      class: "w-full mt-2", placeholder: "Paid Price" %>
      <%= product_form.number_field      :original_price,  class: "w-full mt-2", placeholder: "Original Price (optional)" %>
      <%= product_form.date_field        :production_date, class: "w-full mt-2", placeholder: "Production Date" %>
      <%= product_form.date_field        :expiry_date,     class: "w-full mt-2", placeholder: "Expiry Date" %>

      <div class="mt-2">
        <%= product_form.check_box :_destroy %>
        <%= product_form.label :_destroy, "Remove this product" %>
      </div>
    <% end %>
  </div>

  <!-- Button to add new product fields dynamically using JavaScript -->
  <div class="my-5 border-b-2 relative">
    <button class="h-12 w-12 absolute top-0 -translate-y-1/2 right-0" onclick="addProduct(event)">
      <%= render partial: 'shared/add_button' %>
    </button>
  </div>
<% end %>

<script>
  let productTemplate;

  function onInit() {
    productTemplate = document.querySelector('[data-template="product"]');
    productTemplate.classList.remove('hidden');
    productTemplate.parentNode.removeChild(productTemplate);
  }

  function addProduct(event) {
    event.preventDefault();

    const productsContainer = document.querySelector('#products-container');

    // Generate a unique index based on current children count
    const index = productsContainer.children.length + 1;

    const newProductFields = productTemplate.cloneNode(true);
    // Update name and id attributes in the cloned template
    newProductFields.querySelectorAll('input, select').forEach((field) => {
      if (field.name) {
        // Replace the index in the name attribute (if any exists)
        field.name = field.name.replace(/\[\d+\]/, `[${index}]`);
      }
      if (field.id) {
        // Generate a new id based on the index
        const newId = `${field.name.replace(/[\[\]]+/g, '_')}`;
        field.id = newId;
      }
    });

    // Append the cloned template to the products container
    productsContainer.appendChild(newProductFields);
  }

  // Run the initialization when the DOM is fully loaded
  document.addEventListener('DOMContentLoaded', onInit);
</script>
