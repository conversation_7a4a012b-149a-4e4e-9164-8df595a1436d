<% gift_event = model %>

<%= render 'shared/model_form', model: gift_event, locals: { data: { controller: 'gift-event-form'} } do |form| %>
  <%= render 'shared/date_field_with_update',
    form: form,
    field_name: :date,
    controller_name: 'gift-event-form',
    update_method: 'updateAvailableEggs',
    model: gift_event %>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:persona_id, @available_personas, :id, :nickname, { prompt: "To whom was it gifted?" }, { class: "flex-1" }) %>
    <%= render partial: 'shared/add_button', locals: { path: new_persona_path } if current_user&.contributor_or_admin? %>
  </div>

  <div class="my-5 h-48 overflow-auto">
    <ul class="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-1 lg:gap-2">
      <%= turbo_frame_tag :available_eggs do %>
        <%= collection_check_boxes(:gift_event, :egg_ids, @available_eggs, :id, :id, {}, { multiple: true }) do |choice| %>
          <li class="flex items-center gap-2 bg-gray-100 p-1 rounded">
            <%= choice.check_box class: 'p-4' %>
            <%= choice.label class: 'flex-1 overflow-hidden text-base' %>
          </li>
        <% end %>
      <% end %>
    </ul>
  </div>
<% end %>
