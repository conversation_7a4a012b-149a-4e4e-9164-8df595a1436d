class SellEventsController < ApplicationController
  before_action :set_sell_event, only: %i[show edit update destroy]
  before_action :set_available_eggs, only: %i[new edit]
  before_action :set_available_personas, only: %i[new edit]

  # GET /sell_events or /sell_events.json
  def index
    @sell_events = SellEvent.order(date: :desc)
  end

  # GET /sell_events/1 or /sell_events/1.json
  def show; end

  # GET /sell_events/new
  def new
    @sell_event = SellEvent.new
  end

  # GET /sell_events/1/edit
  def edit; end

  # POST /sell_events or /sell_events.json
  def create
    @sell_event = SellEvent.new(sell_event_params)

    respond_to do |format|
      if @sell_event.save
        format.html { redirect_to sell_event_url(@sell_event), notice: 'Sell event was successfully created.' }
        format.json { render :show, status: :created, location: @sell_event }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @sell_event.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /sell_events/1 or /sell_events/1.json
  def update
    respond_to do |format|
      if @sell_event.update(sell_event_params)
        format.html { redirect_to sell_event_url(@sell_event), notice: 'Sell event was successfully updated.' }
        format.json { render :show, status: :ok, location: @sell_event }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @sell_event.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /sell_events/1 or /sell_events/1.json
  def destroy
    @sell_event.destroy!

    respond_to do |format|
      format.html { redirect_to sell_events_url, notice: 'Sell event was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_available_personas
    @available_personas = Persona.order(:nickname)
  end

  def set_available_eggs
    date = begin
      DateTime.parse sell_event_params[:date]
    rescue StandardError
      @sell_event&.date || 1.second.ago
    end

    not_evented_eggs = Egg
      .where(food_event_id: nil, gift_event_id: nil, sell_event_id: nil)
      .where(laid_at: (date - 3.months).beginning_of_day..date.end_of_day)
      .order(id: :desc)

    already_included_eggs = Egg.where(sell_event_id: @sell_event&.id || -1)

    @available_eggs = not_evented_eggs.or(already_included_eggs)
  end

  def set_sell_event
    @sell_event = SellEvent.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def sell_event_params
    params.require(:sell_event).permit(
      :date,
      :persona_id,
      :price,
      egg_ids: []
    )
  end
end
