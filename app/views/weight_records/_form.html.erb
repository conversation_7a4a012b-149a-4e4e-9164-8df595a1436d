<% weight_record = model %>

<%= render 'shared/model_form', model: weight_record, locals: { data: { controller: "weight-record-form" } } do |form| %>
  <div class="my-5 flex">
    <%= form.button 'Update',
      formaction:new_weight_record_path,
      formmethod: :get,
      style: 'display: none',
      data: {
        weight_record_form_target: 'updateAvailableChickens',
        turbo_frame: :available_chickens
      } %>

    <%= turbo_frame_tag :available_chickens do %>
      <%= form.collection_select(
          :entity_id,
          @available_chickens,
          :id,
          :name,
          { prompt: 'Choose a chicken' },
          { class: 'flex-1' }
        ) %>
    <% end %>
  </div>

  <div class="my-5">
    <%= form.label :date, 'Sampled on' %>
    <%= form.datetime_field :date,
      class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full",
      data: {
        action: 'change->weight-record-form#updateAvailableChickens',
        default_date: 'now'
      } %>
  </div>

  <div class="my-5">
    <%= form.label :value, 'Weight in grams' %>
    <%= form.number_field :value, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>
<% end %>
