require 'rails_helper'

RSpec.describe Chicken, type: :model do
  subject { described_class.new(name: '<PERSON><PERSON>') }

  context 'validations' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_uniqueness_of(:name) }
  end

  context 'associations' do
    it { is_expected.to belong_to(:gender) }
    it { is_expected.to have_and_belong_to_many(:eggs) }
    it { is_expected.to have_many(:vet_visits).dependent(:destroy) }
    it { is_expected.to have_many(:weight_records).dependent(:destroy) }
    it { is_expected.to have_many_attached(:images) }
  end

  context 'scopes' do
    describe '.female' do
      it 'returns only female chickens' do
        female_chicken = create(:chicken, name: '<PERSON>', gender: Gender.female)
        male_chicken = create(:chicken, name: 'Rooster', gender: Gender.male)

        expect(described_class.female).to include(female_chicken)
        expect(described_class.female).not_to include(male_chicken)
      end
    end

    describe '.male' do
      it 'returns only male chickens' do
        female_chicken = create(:chicken, name: '<PERSON>', gender: Gender.female)
        male_chicken = create(:chicken, name: 'Rooster', gender: Gender.male)

        expect(described_class.male).to include(male_chicken)
        expect(described_class.male).not_to include(female_chicken)
      end
    end
  end
end
