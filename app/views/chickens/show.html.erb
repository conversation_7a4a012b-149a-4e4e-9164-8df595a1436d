<%= render 'shared/show_page', model: @chicken do %>
  <% if @chicken.weight_records.any? %>
    <%= render partial: 'shared/chart',
                locals: {
                  categorized_coordinates: @chicken_weight_categorized_coordinates,
                  chart_id: 'chicken-weight-chart',
                  chart_title: 'Body Weight',
                  timespan_selector_id: 'chicken-weight-chart-time-span-selector',
                  vertical_axis_title: 'Weight (kg)',
                  default_timespan: ChartTimespanOptions::ENTIRE_HISTORY,
                } %>
  <% end %>

  <% if @chicken.eggs.weighted.any? %>
    <%= render partial: 'shared/chart',
                locals: {
                  categorized_coordinates: @chicken_egg_weight_categorized_coordinates,
                  chart_id: 'chicken-egg-weight-chart',
                  chart_title: 'Egg Weight',
                  timespan_selector_id: 'chicken-egg-weight-chart-time-span-selector',
                  default_timespan: ChartTimespanOptions::ENTIRE_HISTORY,
                } %>
  <% end %>
<% end %>
