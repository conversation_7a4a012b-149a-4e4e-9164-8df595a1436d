require 'rails_helper'

RSpec.describe Document, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:document_type) }
    it { is_expected.to belong_to(:issuer).class_name('Persona') }
    it { is_expected.to have_many(:image_associations).dependent(:nullify) }
    it { is_expected.to have_many(:images).through(:image_associations) }
    it { is_expected.to have_and_belong_to_many(:holders).class_name('Persona').inverse_of(:documents) }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:title) }
    it { is_expected.to validate_length_of(:title).is_at_least(1).is_at_most(256) }
  end
end
