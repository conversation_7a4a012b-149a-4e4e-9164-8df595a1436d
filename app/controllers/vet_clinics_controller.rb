class VetClinicsController < ApplicationController
  before_action :set_vet_clinic, only: %i[show edit update destroy]

  # GET /vet_clinics or /vet_clinics.json
  def index
    @vet_clinics = authorize VetClinic.order(:name)
  end

  # GET /vet_clinics/1 or /vet_clinics/1.json
  def show
    authorize @vet_clinic
  end

  # GET /vet_clinics/new
  def new
    @vet_clinic = authorize VetClinic.new
  end

  # GET /vet_clinics/1/edit
  def edit
    authorize @vet_clinic
  end

  # POST /vet_clinics or /vet_clinics.json
  def create
    @vet_clinic = authorize VetClinic.new(vet_clinic_params)

    respond_to do |format|
      if @vet_clinic.save
        format.html { redirect_to vet_clinic_url(@vet_clinic), notice: 'Vet clinic was successfully created.' }
        format.json { render :show, status: :created, location: @vet_clinic }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @vet_clinic.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /vet_clinics/1 or /vet_clinics/1.json
  def update
    respond_to do |format|
      if (authorize @vet_clinic).update(vet_clinic_params)
        format.html { redirect_to vet_clinic_url(@vet_clinic), notice: 'Vet clinic was successfully updated.' }
        format.json { render :show, status: :ok, location: @vet_clinic }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @vet_clinic.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /vet_clinics/1 or /vet_clinics/1.json
  def destroy
    (authorize @vet_clinic).destroy!

    respond_to do |format|
      format.html { redirect_to vet_clinics_url, notice: 'Vet clinic was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_vet_clinic
    @vet_clinic = VetClinic.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def vet_clinic_params
    params.require(:vet_clinic).permit(:name, :city_id, :address)
  end
end
