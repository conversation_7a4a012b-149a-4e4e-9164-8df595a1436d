<% as_background ||= false %>

<div
  class="
    circular border-2
    <%= 'p-2' unless chicken.has_own_avatar? %>
    <%= klass if defined?(klass) %>
  "
  style="
    background-color: <%= chicken.color %>;
    border-color: <%= chicken.color %>;
    <%= "background-image: url('#{url_for(chicken.avatar)}')" if as_background %>;
    background-size: cover;

    box-shadow: 3px 3px 6px rgba(0,0,0,0.5) inset, -2px -2px 4px rgba(255,255,255,0.25) inset;

    <%= style.map { |k, v| "#{k}: #{v};" }.join(' ') if defined?(style) && style.is_a?(Hash) %>
  "
  title="<%= chicken.name %>">
  <%= image_tag chicken.avatar, loading: 'lazy' unless as_background %>
</div>
