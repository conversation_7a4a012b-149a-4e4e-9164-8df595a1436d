class DocumentsController < ApplicationController
  before_action :set_document, only: %i[show edit update destroy]
  before_action :set_available_document_types, only: %i[new create edit update]
  before_action :set_available_personas, only: %i[new create edit update]

  # GET /documents or /documents.json
  def index
    @documents = authorize Document.order(:id)
                                   .then(&apply_pagination(per_page: 12))
  end

  # GET /documents/1 or /documents/1.json
  def show
    authorize @document
  end

  # GET /documents/new
  def new
    @document = authorize Document.new
  end

  # GET /documents/1/edit
  def edit
    authorize @document
  end

  # POST /documents or /documents.json
  def create
    @document = authorize Document.new(document_params.except(:images))

    respond_to do |format|
      if @document.save
        attach_images

        format.html { redirect_to document_url(@document), notice: 'Document was successfully created.' }
        format.json { render :show, status: :created, location: @document }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @document.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /documents/1 or /documents/1.json
  def update
    respond_to do |format|
      if (authorize @document).update(document_params.except(:images))
        attach_images

        format.html { redirect_to document_url(@document), notice: 'Document was successfully updated.' }
        format.json { render :show, status: :ok, location: @document }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @document.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /documents/1 or /documents/1.json
  def destroy
    (authorize @document).destroy!

    respond_to do |format|
      format.html { redirect_to documents_url, notice: 'Document was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  def attach_images
    params[:document][:images]&.each do |file|
      next if file.blank?

      ImageAssociation.create(image: Image.create(file:), imageable: @document)
    end
  end

  def set_document
    @document = Document.find(params[:id])
  end

  def set_available_document_types
    @available_document_types = DocumentType.order(:title)
  end

  def set_available_personas
    @available_personas = Persona.order(:nickname)
  end

  # Only allow a list of trusted parameters through.
  def document_params
    params.require(:document).permit(
      :description,
      :document_type_id,
      :expires_on,
      :issued_on,
      :issuer_id,
      :title,
      holder_ids: [],
      images: []
    )
  end
end
