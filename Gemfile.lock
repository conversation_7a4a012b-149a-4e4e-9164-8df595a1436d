GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    addressable (2.8.6)
      public_suffix (>= 2.0.2, < 6.0)
    ast (2.4.2)
    aws-eventstream (1.3.0)
    aws-partitions (1.887.0)
    aws-sdk-core (3.191.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.8)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.77.0)
      aws-sdk-core (~> 3, >= 3.191.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.143.0)
      aws-sdk-core (~> 3, >= 3.191.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.8)
    aws-sigv4 (1.8.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark (0.4.0)
    better_html (2.1.1)
      actionview (>= 6.0)
      activesupport (>= 6.0)
      ast (~> 2.0)
      erubi (~> 1.4)
      parser (>= 2.4)
      smart_properties
    bigdecimal (3.1.6)
    bindex (0.8.1)
    bootsnap (1.18.3)
      msgpack (~> 1.2)
    brakeman (5.4.1)
    builder (3.2.4)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    coderay (1.1.3)
    concurrent-ruby (1.3.5)
    connection_pool (2.4.1)
    crass (1.0.6)
    date (3.3.4)
    debug (1.9.1)
      irb (~> 1.10)
      reline (>= 0.3.8)
    devise (4.9.3)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.6.1)
    drb (2.2.0)
      ruby2_keywords
    erb_lint (0.5.0)
      activesupport
      better_html (>= 2.0.1)
      parser (>= *******)
      rainbow
      rubocop
      smart_properties
    erubi (1.12.0)
    factory_bot (6.5.1)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    ffi (1.16.3)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.1)
      concurrent-ruby (~> 1.0)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    importmap-rails (2.0.1)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.7.2)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    json (2.7.1)
    language_server-protocol (********)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.2)
    matrix (0.4.2)
    method_source (1.1.0)
    mini_magick (4.12.0)
    mini_mime (1.1.5)
    minitest (5.22.1)
    msgpack (1.7.2)
    net-imap (0.4.10)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (*******)
      net-protocol
    nio4r (2.7.0)
    nokogiri (1.16.2-aarch64-linux)
      racc (~> 1.4)
    nokogiri (1.16.2-x86_64-linux)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    parallel (1.24.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.4)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    psych (5.1.2)
      stringio
    public_suffix (5.0.4)
    puma (6.4.2)
      nio4r (~> 2.0)
    pundit (2.3.1)
      activesupport (>= 3.0.0)
    racc (1.7.3)
    rack (3.0.9)
    rack-session (2.0.0)
      rack (>= 3.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rackup (2.1.0)
      rack (>= 3)
      webrick (~> 1.8)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.1.0)
    rdoc (6.6.2)
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.4.2)
      io-console (~> 0.5)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.2.6)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (8.0.0)
      actionpack (>= 7.2)
      activesupport (>= 7.2)
      railties (>= 7.2)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.3)
    rubocop (1.75.5)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-discourse (3.6.1)
      rubocop (>= 1.59.0)
      rubocop-rspec (>= 2.25.0)
    rubocop-factory_bot (2.27.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.23.1)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.33.0, < 2.0)
      rubocop-ast (>= 1.30.0, < 2.0)
    rubocop-rspec (3.6.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.0)
      ffi (~> 1.12)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    securerandom (0.4.1)
    selenium-webdriver (4.10.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    shoulda-matchers (6.5.0)
      activesupport (>= 5.2.0)
    smart_properties (1.17.0)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    stimulus-rails (1.3.3)
      railties (>= 6.0.0)
    stringio (3.1.0)
    tailwindcss-rails (2.3.0-aarch64-linux)
      railties (>= 6.0.0)
    tailwindcss-rails (2.3.0-x86_64-linux)
      railties (>= 6.0.0)
    thor (1.3.0)
    timeout (0.4.1)
    turbo-rails (2.0.0)
      actionpack (>= 6.0.0)
      activejob (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (2.5.0)
    useragent (0.16.11)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    webrick (1.8.1)
    websocket (1.2.10)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    will_paginate (3.3.1)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.13)

PLATFORMS
  aarch64-linux
  x86_64-linux

DEPENDENCIES
  aws-sdk-s3 (~> 1.119)
  bootsnap
  brakeman (~> 5.4, >= 5.4.1)
  capybara
  debug
  devise (~> 4.9, >= 4.9.1)
  erb_lint (~> 0.5.0)
  factory_bot_rails (~> 6.2)
  image_processing (~> 1.12.2)
  importmap-rails
  jbuilder (~> 2.11, >= 2.11.5)
  pg (~> 1.5, >= 1.5.3)
  pry (~> 0.14.2)
  puma (~> 6.2, >= 6.2.2)
  pundit (~> 2.3)
  rails (~> 7.0, >= 7.0.5)
  rspec-rails (~> 8.0)
  rubocop (~> 1.51)
  rubocop-discourse (~> 3.2)
  rubocop-factory_bot (~> 2.27, >= 2.27.1)
  rubocop-performance (~> 1.25)
  rubocop-rails (~> 2.19, >= 2.19.1)
  rubocop-rspec (~> 3.6)
  selenium-webdriver
  shoulda-matchers (~> 6.5)
  sprockets-rails
  stimulus-rails
  tailwindcss-rails
  turbo-rails
  tzinfo-data
  web-console
  webdrivers
  will_paginate (~> 3.3, >= 3.3.1)

RUBY VERSION
   ruby 3.2.2p53

BUNDLED WITH
   2.4.10
