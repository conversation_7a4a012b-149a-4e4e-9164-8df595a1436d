class RefuelEventsController < ApplicationController
  before_action :set_refuel_event, only: %i[show edit update destroy]

  # GET /refuel_events or /refuel_events.json
  def index
    @refuel_events = authorize RefuelEvent.all
                                          .then(&apply_pagination(per_page: 12))
  end

  # GET /refuel_events/1 or /refuel_events/1.json
  def show
    authorize @refuel_event
  end

  # GET /refuel_events/new
  def new
    volume = UnitCategory.find_or_create_by(name: 'volume')
    liters = Unit.find_or_create_by(name: 'liter', sign: 'l', unit_category: volume)
    euros = Currency.find_or_create_by(name: 'euros', sign: '€')

    @refuel_event = authorize RefuelEvent.new(unit_id: liters.id, currency_id: euros.id)
  end

  # GET /refuel_events/1/edit
  def edit
    authorize @refuel_event
  end

  # POST /refuel_events or /refuel_events.json
  def create
    @refuel_event = authorize RefuelEvent.new(refuel_event_params)

    respond_to do |format|
      if @refuel_event.save
        format.html { redirect_to refuel_event_url(@refuel_event), notice: 'Refuel event was successfully created.' }
        format.json { render :show, status: :created, location: @refuel_event }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @refuel_event.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /refuel_events/1 or /refuel_events/1.json
  def update
    respond_to do |format|
      if (authorize @refuel_event).update(refuel_event_params)
        format.html { redirect_to refuel_event_url(@refuel_event), notice: 'Refuel event was successfully updated.' }
        format.json { render :show, status: :ok, location: @refuel_event }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @refuel_event.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /refuel_events/1 or /refuel_events/1.json
  def destroy
    (authorize @refuel_event).destroy!

    respond_to do |format|
      format.html { redirect_to refuel_events_url, notice: 'Refuel event was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_refuel_event
    @refuel_event = RefuelEvent.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def refuel_event_params
    params.require(:refuel_event).permit(
      :refuel_station_id,
      :refuled_at,
      :amount,
      :paid_price,
      :price_per_unit,
      :currency_id,
      :unit_id
    )
  end
end
