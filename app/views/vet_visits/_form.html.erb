<% vet_visit = model %>

<%= render 'shared/model_form', model: vet_visit, locals: { data: { controller: 'vet-visit-form' } } do |form| %>
  <div class="my-5 flex gap-2">
    <%= form.collection_select(:vet_id, @available_vets, :id, :nickname, { prompt: "Which vet?" }, { class: "flex-1" }) %>
    <%= render partial: 'shared/add_button', locals: { path: new_persona_path } if current_user&.contributor_or_admin? %>
  </div>

  <div class="my-5">
    <%= form.label :date %>
    <%= form.datetime_field :date,
      class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full",
      data: {
        action: 'change->vet-visit-form#updateAvailableChickens',
        default_date: 'now'
      } %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:vet_clinic_id, @available_vet_clinics, :id, :name, { prompt: "Which clinic?" }, { class: "flex-1" }) %>
    <%= render partial: 'shared/add_button', locals: { path: new_vet_clinic_path } if current_user&.contributor_or_admin? %>
  </div>

  <div class="grid grid-cols-[auto_1fr] gap-x-2 gap-y-4 items-center">
    <%= form.check_box :at_home, class: "h-5 w-5" %>
    <%= form.label :at_home %>

    <%= form.check_box :injection_in_place, class: "h-5 w-5" %>
    <%= form.label :injection_in_place %>

    <%= form.check_box :medicine_in_place, class: "h-5 w-5" %>
    <%= form.label :medicine_in_place %>

    <%= form.check_box :has_medicine_routine, class: "h-5 w-5" %>
    <%= form.label :has_medicine_routine %>

    <%= form.check_box :needs_second_visit, class: "h-5 w-5" %>
    <%= form.label :needs_second_visit %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.button 'Update',
        formaction:new_vet_visit_path,
        formmethod: :get,
        style: 'display: none',
        data: {
          vet_visit_form_target: 'updateAvailableChickens',
          turbo_frame: :available_chickens
        } %>

    <%= turbo_frame_tag :available_chickens do %>
      <%= form.hidden_field :chicken_id, value: "" %>
      <%= form.collection_select(:chicken_id, @available_chickens, :id, :name, { prompt: "Which chicken?" }, { class: "flex-1" }) %>
      <%= render partial: 'shared/add_button', locals: { path: new_chicken_path } if current_user&.contributor_or_admin? %>
    <% end %>
  </div>

  <div class="my-5">
    <%= form.label :situation_description %>
    <%= form.text_area :situation_description, rows: 4, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5">
    <%= form.label :doctor_verdict %>
    <%= form.text_area :doctor_verdict, rows: 4, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5">
    <%= form.label :prescription %>
    <%= form.text_area :prescription, rows: 4, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5">
    <%= form.label :severity %>
    <div class="flex flex-col lg:flex-row flex-wrap gap-2 lg:gap-4">
      <% VetVisit.severities.keys.each do |severity| %>
        <label class="flex items-center space-x-2">
          <%= form.radio_button :severity, severity, class: "h-5 w-5" %>
          <span><%= severity.humanize %></span>
        </label>
      <% end %>
    </div>
  </div>

  <div class="my-5">
    <%= form.label :previous_visit_id %>
    <%= form.collection_select(:previous_visit_id, @available_previous_visits, :id, ->(visit) { "#{I18n.l(visit.date, format: :abbreviated)} by #{visit.vet.nickname}" }, { prompt: "If it is a second visit, then when was the previous visit?" }, { class: "flex-1" }) %>
  </div>
<% end %>
