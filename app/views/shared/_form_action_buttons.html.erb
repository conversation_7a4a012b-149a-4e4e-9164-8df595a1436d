<%
  list_path ||= polymorphic_path([model.class])
  # Remove the line below, when confident with the line above
  # list_path ||= polymorphic_path(model.class.model_name.route_key.to_sym)
%>

<div class="flex gap-2 justify-between">
  <div class="flex gap-2">
    <%= render partial: 'shared/back_button', locals: { target: model.persisted? ? model : list_path } %>
    <%= render partial: 'shared/index_button', locals: { target: list_path } %>

    <%= other_buttons if defined?(other_buttons) %>
  </div>

  <%= button_tag type: 'submit', class: 'h-12' do %>
    <%= image_tag 'confirm-icon.png', class: 'w-8 invert mix-blend-screen', loading: 'lazy' %>
    <span class="text-xl font-semibold"><%= model.persisted? ? 'Update' : 'Create' %></span>
  <% end %>
</div>
